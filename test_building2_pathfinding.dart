import 'lib/models/building2_location.dart';
import 'lib/services/building2_pathfinding_service.dart';

void main() {
  print('=== Testing Building 2 Pathfinding Direction Fix ===\n');

  // Test Case 1: CAS 202 (reversed) to Chemistry Lab 2
  print('Test 1: CAS 202 (2clage2_r) to Chemistry Lab 2 (3clage2)');
  testRoute(2, 2, true, 3, 2, false,
      'Should: move to HR, go up stairs, move forward to Chem Lab 2');

  print('\n' + '=' * 50 + '\n');

  // Test Case 2: Human Resources (reversed) to CAS 305
  print('Test 2: Human Resources (2clage1_r) to CAS 305 (3clage5)');
  testRoute(2, 1, true, 3, 5, false,
      'Should: go up stairs, move forward to CAS 305 (no turn around after stairs)');
}

void testRoute(int fromFloor, int fromClage, bool fromReversed, int toFloor,
    int toClage, bool toReversed, String expected) {
  final fromLocation = Building2LocationData.findLocationByPosition(
      fromFloor, fromClage,
      isReversed: fromReversed);
  final toLocation = Building2LocationData.findLocationByPosition(
      toFloor, toClage,
      isReversed: toReversed);

  if (fromLocation == null || toLocation == null) {
    print('Error: Could not find locations');
    print('From: $fromLocation');
    print('To: $toLocation');
    return;
  }

  print(
      'From: ${fromLocation.displayName} (${fromFloor}clage${fromClage}${fromReversed ? '_r' : ''})');
  print(
      'To: ${toLocation.displayName} (${toFloor}clage${toClage}${toReversed ? '_r' : ''})');
  print('Expected: $expected\n');

  // Find the shortest path
  final route =
      Building2PathfindingService.findShortestPath(fromLocation, toLocation);

  if (route == null) {
    print('Error: No route found');
    return;
  }

  print('Route found with ${route.totalSteps} steps:');
  for (int i = 0; i < route.steps.length; i++) {
    final step = route.steps[i];
    print('${i + 1}. ${step.instruction}');
  }

  // Check for problematic patterns
  bool hasWrongTurnAround = false;
  bool hasWrongDirection = false;

  for (int i = 0; i < route.steps.length; i++) {
    final step = route.steps[i];

    // Check for "turn around" immediately after stairs
    if (i > 0 &&
            route.steps[i - 1].direction ==
                Building2NavigationDirection.upFloor ||
        route.steps[i - 1].direction ==
            Building2NavigationDirection.downFloor) {
      if (step.direction == Building2NavigationDirection.reverse) {
        hasWrongTurnAround = true;
        print(
            '  ⚠️  ISSUE: Turn around suggested immediately after stairs (step ${i + 1})');
      }
    }

    // Check for wrong directions (this is harder to detect automatically)
    if (step.instruction.contains('move backward') &&
        step.from.floor == step.to.floor &&
        step.to.clage > step.from.clage) {
      hasWrongDirection = true;
      print(
          '  ⚠️  ISSUE: Says "move backward" but going to higher clage (step ${i + 1})');
    }
  }

  if (!hasWrongTurnAround && !hasWrongDirection) {
    print('✅ No obvious direction issues detected!');
  }
}

class Building1Location {
  final String id;
  final String name;
  final String displayName;
  final int floor;
  final int main;
  final bool isReversed;
  final List<String> keywords;
  final List<String> aliases;

  const Building1Location({
    required this.id,
    required this.name,
    required this.displayName,
    required this.floor,
    required this.main,
    required this.isReversed,
    this.keywords = const [],
    this.aliases = const [],
  });

  @override
  String toString() => displayName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Building1Location &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class Building1LocationData {
  static const List<Building1Location> allLocations = [
    // Floor 1 (main0-main25)
    Building1Location(
      id: 'f1_m0',
      name: 'Accounting/Admissions Office',
      displayName: 'Accounting/Admissions Office',
      floor: 1,
      main: 0,
      isReversed: false,
      keywords: ['accounting', 'admissions', 'office', 'entrance', 'main'],
      aliases: ['accounting office', 'admissions office', 'main entrance'],
    ),
    Building1Location(
      id: 'f1_m1',
      name: 'Finance Office',
      displayName: 'Finance Office',
      floor: 1,
      main: 1,
      isReversed: false,
      keywords: ['finance', 'office', 'financial'],
      aliases: ['finance office', 'financial office'],
    ),
    Building1Location(
      id: 'f1_m1_r',
      name: 'Finance Office (Reversed)',
      displayName: 'Finance Office (Reversed)',
      floor: 1,
      main: 1,
      isReversed: true,
      keywords: ['finance', 'office', 'financial', 'reversed'],
      aliases: ['finance office reversed', 'financial office reversed'],
    ),
    Building1Location(
      id: 'f1_m2',
      name: 'Finance Office',
      displayName: 'Finance Office',
      floor: 1,
      main: 2,
      isReversed: false,
      keywords: ['finance', 'office', 'financial'],
      aliases: ['finance office', 'financial office'],
    ),
    Building1Location(
      id: 'f1_m2_r',
      name: 'Finance Office (Reversed)',
      displayName: 'Finance Office (Reversed)',
      floor: 1,
      main: 2,
      isReversed: true,
      keywords: ['finance', 'office', 'financial', 'reversed'],
      aliases: ['finance office reversed', 'financial office reversed'],
    ),
    Building1Location(
      id: 'f1_m3',
      name: 'Canteen',
      displayName: 'Canteen',
      floor: 1,
      main: 3,
      isReversed: false,
      keywords: ['canteen', 'food', 'cafeteria', 'dining'],
      aliases: ['cafeteria', 'dining hall', 'food court'],
    ),
    Building1Location(
      id: 'f1_m3_r',
      name: 'Canteen (Reversed)',
      displayName: 'Canteen (Reversed)',
      floor: 1,
      main: 3,
      isReversed: true,
      keywords: ['canteen', 'food', 'cafeteria', 'dining', 'reversed'],
      aliases: ['cafeteria reversed', 'dining hall reversed'],
    ),
    Building1Location(
      id: 'f1_m4',
      name: 'Canteen',
      displayName: 'Canteen',
      floor: 1,
      main: 4,
      isReversed: false,
      keywords: ['canteen', 'food', 'cafeteria', 'dining'],
      aliases: ['cafeteria', 'dining hall', 'food court'],
    ),
    Building1Location(
      id: 'f1_m4_r',
      name: 'Canteen (Reversed)',
      displayName: 'Canteen (Reversed)',
      floor: 1,
      main: 4,
      isReversed: true,
      keywords: ['canteen', 'food', 'cafeteria', 'dining', 'reversed'],
      aliases: ['cafeteria reversed', 'dining hall reversed'],
    ),
    Building1Location(
      id: 'f1_m5',
      name: 'Canteen',
      displayName: 'Canteen',
      floor: 1,
      main: 5,
      isReversed: false,
      keywords: ['canteen', 'food', 'cafeteria', 'dining'],
      aliases: ['cafeteria', 'dining hall', 'food court'],
    ),
    Building1Location(
      id: 'f1_m5_r',
      name: 'Canteen (Reversed)',
      displayName: 'Canteen (Reversed)',
      floor: 1,
      main: 5,
      isReversed: true,
      keywords: ['canteen', 'food', 'cafeteria', 'dining', 'reversed'],
      aliases: ['cafeteria reversed', 'dining hall reversed'],
    ),
    Building1Location(
      id: 'f1_m6',
      name: 'Canteen - Hallway',
      displayName: 'Canteen - Hallway',
      floor: 1,
      main: 6,
      isReversed: false,
      keywords: ['canteen', 'hallway', 'corridor', 'food'],
      aliases: ['canteen hallway', 'canteen corridor'],
    ),
    Building1Location(
      id: 'f1_m6_r',
      name: 'Canteen - Hallway (Reversed)',
      displayName: 'Canteen - Hallway (Reversed)',
      floor: 1,
      main: 6,
      isReversed: true,
      keywords: ['canteen', 'hallway', 'corridor', 'food', 'reversed'],
      aliases: ['canteen hallway reversed', 'canteen corridor reversed'],
    ),
    Building1Location(
      id: 'f1_m7',
      name: 'Canteen - Hallway',
      displayName: 'Canteen - Hallway',
      floor: 1,
      main: 7,
      isReversed: false,
      keywords: ['canteen', 'hallway', 'corridor', 'food'],
      aliases: ['canteen hallway', 'canteen corridor'],
    ),
    Building1Location(
      id: 'f1_m7_r',
      name: 'Canteen - Hallway (Reversed)',
      displayName: 'Canteen - Hallway (Reversed)',
      floor: 1,
      main: 7,
      isReversed: true,
      keywords: ['canteen', 'hallway', 'corridor', 'food', 'reversed'],
      aliases: ['canteen hallway reversed', 'canteen corridor reversed'],
    ),
    Building1Location(
      id: 'f1_m8',
      name: 'Canteen - Hallway',
      displayName: 'Canteen - Hallway',
      floor: 1,
      main: 8,
      isReversed: false,
      keywords: ['canteen', 'hallway', 'corridor', 'food', 'stairs'],
      aliases: ['canteen hallway', 'canteen corridor', 'stairs area'],
    ),
    Building1Location(
      id: 'f1_m8_r',
      name: 'Canteen - Hallway (Reversed)',
      displayName: 'Canteen - Hallway (Reversed)',
      floor: 1,
      main: 8,
      isReversed: true,
      keywords: [
        'canteen',
        'hallway',
        'corridor',
        'food',
        'reversed',
        'stairs'
      ],
      aliases: ['canteen hallway reversed', 'canteen corridor reversed'],
    ),
    Building1Location(
      id: 'f1_m9',
      name: 'Book Store',
      displayName: 'Book Store',
      floor: 1,
      main: 9,
      isReversed: false,
      keywords: ['book', 'store', 'bookstore', 'books', 'supplies'],
      aliases: ['bookstore', 'book shop', 'supplies store'],
    ),
    Building1Location(
      id: 'f1_m9_r',
      name: 'Book Store (Reversed)',
      displayName: 'Book Store (Reversed)',
      floor: 1,
      main: 9,
      isReversed: true,
      keywords: ['book', 'store', 'bookstore', 'books', 'supplies', 'reversed'],
      aliases: ['bookstore reversed', 'book shop reversed'],
    ),
    Building1Location(
      id: 'f1_m10',
      name: 'Book Store',
      displayName: 'Book Store',
      floor: 1,
      main: 10,
      isReversed: false,
      keywords: ['book', 'store', 'bookstore', 'books', 'supplies'],
      aliases: ['bookstore', 'book shop', 'supplies store'],
    ),
    Building1Location(
      id: 'f1_m10_r',
      name: 'Book Store (Reversed)',
      displayName: 'Book Store (Reversed)',
      floor: 1,
      main: 10,
      isReversed: true,
      keywords: ['book', 'store', 'bookstore', 'books', 'supplies', 'reversed'],
      aliases: ['bookstore reversed', 'book shop reversed'],
    ),
    Building1Location(
      id: 'f1_m11',
      name: 'Main 11',
      displayName: 'Main 11',
      floor: 1,
      main: 11,
      isReversed: false,
      keywords: ['main11', 'corridor', 'turn'],
      aliases: ['main 11', 'corridor 11'],
    ),
    Building1Location(
      id: 'f1_m11_r',
      name: 'Main 11 Reversed',
      displayName: 'Main 11 (Reversed)',
      floor: 1,
      main: 11,
      isReversed: true,
      keywords: ['main11', 'corridor', 'reversed', 'turn'],
      aliases: ['main 11 reversed', 'corridor 11 reversed'],
    ),
    Building1Location(
      id: 'f1_m12',
      name: 'Main 12',
      displayName: 'Main 12',
      floor: 1,
      main: 12,
      isReversed: false,
      keywords: ['main12', 'corridor', 'turn'],
      aliases: ['main 12', 'corridor 12'],
    ),
    Building1Location(
      id: 'f1_m12_r',
      name: 'Main 12 Reversed',
      displayName: 'Main 12 (Reversed)',
      floor: 1,
      main: 12,
      isReversed: true,
      keywords: ['main12', 'corridor', 'reversed', 'turn'],
      aliases: ['main 12 reversed', 'corridor 12 reversed'],
    ),
    Building1Location(
      id: 'f1_m13',
      name: 'Main 13',
      displayName: 'Main 13',
      floor: 1,
      main: 13,
      isReversed: false,
      keywords: ['main13', 'corridor'],
      aliases: ['main 13', 'corridor 13'],
    ),
    Building1Location(
      id: 'f1_m13_r',
      name: 'Main 13 Reversed',
      displayName: 'Main 13 (Reversed)',
      floor: 1,
      main: 13,
      isReversed: true,
      keywords: ['main13', 'corridor', 'reversed'],
      aliases: ['main 13 reversed', 'corridor 13 reversed'],
    ),
    Building1Location(
      id: 'f1_m14',
      name: 'Main 14',
      displayName: 'Main 14',
      floor: 1,
      main: 14,
      isReversed: false,
      keywords: ['main14', 'corridor'],
      aliases: ['main 14', 'corridor 14'],
    ),
    Building1Location(
      id: 'f1_m14_r',
      name: 'Main 14 Reversed',
      displayName: 'Main 14 (Reversed)',
      floor: 1,
      main: 14,
      isReversed: true,
      keywords: ['main14', 'corridor', 'reversed'],
      aliases: ['main 14 reversed', 'corridor 14 reversed'],
    ),
    Building1Location(
      id: 'f1_m15',
      name: 'Main 15',
      displayName: 'Main 15',
      floor: 1,
      main: 15,
      isReversed: false,
      keywords: ['main15', 'corridor', 'turn', 'stairs'],
      aliases: ['main 15', 'corridor 15', 'stairs area'],
    ),
    Building1Location(
      id: 'f1_m15_r',
      name: 'Main 15 Reversed',
      displayName: 'Main 15 (Reversed)',
      floor: 1,
      main: 15,
      isReversed: true,
      keywords: ['main15', 'corridor', 'reversed', 'turn', 'stairs'],
      aliases: ['main 15 reversed', 'corridor 15 reversed'],
    ),
    Building1Location(
      id: 'f1_m16',
      name: 'Main 16',
      displayName: 'Main 16',
      floor: 1,
      main: 16,
      isReversed: false,
      keywords: ['main16', 'corridor'],
      aliases: ['main 16', 'corridor 16'],
    ),
    // Continue with remaining main positions for floor 1
    Building1Location(
      id: 'f1_m17',
      name: 'Main 17',
      displayName: 'Main 17',
      floor: 1,
      main: 17,
      isReversed: false,
      keywords: ['main17', 'corridor'],
      aliases: ['main 17', 'corridor 17'],
    ),
    Building1Location(
      id: 'f1_m17_r',
      name: 'Main 17 Reversed',
      displayName: 'Main 17 (Reversed)',
      floor: 1,
      main: 17,
      isReversed: true,
      keywords: ['main17', 'corridor', 'reversed'],
      aliases: ['main 17 reversed', 'corridor 17 reversed'],
    ),
    Building1Location(
      id: 'f1_m18',
      name: 'Main 18',
      displayName: 'Main 18',
      floor: 1,
      main: 18,
      isReversed: false,
      keywords: ['main18', 'corridor'],
      aliases: ['main 18', 'corridor 18'],
    ),
    Building1Location(
      id: 'f1_m18_r',
      name: 'Main 18 Reversed',
      displayName: 'Main 18 (Reversed)',
      floor: 1,
      main: 18,
      isReversed: true,
      keywords: ['main18', 'corridor', 'reversed'],
      aliases: ['main 18 reversed', 'corridor 18 reversed'],
    ),
    Building1Location(
      id: 'f1_m19',
      name: 'Main 19',
      displayName: 'Main 19',
      floor: 1,
      main: 19,
      isReversed: false,
      keywords: ['main19', 'corridor'],
      aliases: ['main 19', 'corridor 19'],
    ),
    Building1Location(
      id: 'f1_m19_r',
      name: 'Main 19 Reversed',
      displayName: 'Main 19 (Reversed)',
      floor: 1,
      main: 19,
      isReversed: true,
      keywords: ['main19', 'corridor', 'reversed'],
      aliases: ['main 19 reversed', 'corridor 19 reversed'],
    ),
    Building1Location(
      id: 'f1_m20',
      name: 'Main 20',
      displayName: 'Main 20',
      floor: 1,
      main: 20,
      isReversed: false,
      keywords: ['main20', 'corridor'],
      aliases: ['main 20', 'corridor 20'],
    ),
    Building1Location(
      id: 'f1_m20_r',
      name: 'Main 20 Reversed',
      displayName: 'Main 20 (Reversed)',
      floor: 1,
      main: 20,
      isReversed: true,
      keywords: ['main20', 'corridor', 'reversed'],
      aliases: ['main 20 reversed', 'corridor 20 reversed'],
    ),
    Building1Location(
      id: 'f1_m21',
      name: 'Main 21',
      displayName: 'Main 21',
      floor: 1,
      main: 21,
      isReversed: false,
      keywords: ['main21', 'corridor'],
      aliases: ['main 21', 'corridor 21'],
    ),
    Building1Location(
      id: 'f1_m21_r',
      name: 'Main 21 Reversed',
      displayName: 'Main 21 (Reversed)',
      floor: 1,
      main: 21,
      isReversed: true,
      keywords: ['main21', 'corridor', 'reversed'],
      aliases: ['main 21 reversed', 'corridor 21 reversed'],
    ),
    Building1Location(
      id: 'f1_m22',
      name: 'Main 22',
      displayName: 'Main 22',
      floor: 1,
      main: 22,
      isReversed: false,
      keywords: ['main22', 'corridor'],
      aliases: ['main 22', 'corridor 22'],
    ),
    Building1Location(
      id: 'f1_m22_r',
      name: 'Main 22 Reversed',
      displayName: 'Main 22 (Reversed)',
      floor: 1,
      main: 22,
      isReversed: true,
      keywords: ['main22', 'corridor', 'reversed'],
      aliases: ['main 22 reversed', 'corridor 22 reversed'],
    ),
    Building1Location(
      id: 'f1_m23',
      name: 'Main 23',
      displayName: 'Main 23',
      floor: 1,
      main: 23,
      isReversed: false,
      keywords: ['main23', 'corridor'],
      aliases: ['main 23', 'corridor 23'],
    ),
    Building1Location(
      id: 'f1_m23_r',
      name: 'Main 23 Reversed',
      displayName: 'Main 23 (Reversed)',
      floor: 1,
      main: 23,
      isReversed: true,
      keywords: ['main23', 'corridor', 'reversed'],
      aliases: ['main 23 reversed', 'corridor 23 reversed'],
    ),
    Building1Location(
      id: 'f1_m24',
      name: 'Main 24',
      displayName: 'Main 24',
      floor: 1,
      main: 24,
      isReversed: false,
      keywords: ['main24', 'corridor'],
      aliases: ['main 24', 'corridor 24'],
    ),
    Building1Location(
      id: 'f1_m24_r',
      name: 'Main 24 Reversed',
      displayName: 'Main 24 (Reversed)',
      floor: 1,
      main: 24,
      isReversed: true,
      keywords: ['main24', 'corridor', 'reversed'],
      aliases: ['main 24 reversed', 'corridor 24 reversed'],
    ),
    Building1Location(
      id: 'f1_m25',
      name: 'Main 25',
      displayName: 'Main 25',
      floor: 1,
      main: 25,
      isReversed: false,
      keywords: ['main25', 'corridor'],
      aliases: ['main 25', 'corridor 25'],
    ),
    Building1Location(
      id: 'f1_m25_r',
      name: 'Main 25 Reversed',
      displayName: 'Main 25 (Reversed)',
      floor: 1,
      main: 25,
      isReversed: true,
      keywords: ['main25', 'corridor', 'reversed'],
      aliases: ['main 25 reversed', 'corridor 25 reversed'],
    ),

    // Floor 2 (2main0-2main25)
    Building1Location(
      id: 'f2_m0',
      name: '2Main 0',
      displayName: '2Main 0',
      floor: 2,
      main: 0,
      isReversed: false,
      keywords: ['2main0', 'second', 'floor', 'corridor'],
      aliases: ['2main 0', 'second floor start'],
    ),
    Building1Location(
      id: 'f2_m1',
      name: 'IMCI Laboratory',
      displayName: 'IMCI Laboratory',
      floor: 2,
      main: 1,
      isReversed: false,
      keywords: ['imci', 'laboratory', 'lab', 'medical', 'stairs'],
      aliases: ['imci lab', 'medical laboratory'],
    ),
    Building1Location(
      id: 'f2_m1_r',
      name: 'IMCI Laboratory (Reversed)',
      displayName: 'IMCI Laboratory (Reversed)',
      floor: 2,
      main: 1,
      isReversed: true,
      keywords: ['imci', 'laboratory', 'lab', 'medical', 'reversed', 'stairs'],
      aliases: ['imci lab reversed', 'medical laboratory reversed'],
    ),
    Building1Location(
      id: 'f2_m2',
      name: 'Community Health Nursing Laboratory',
      displayName: 'Community Health Nursing Laboratory',
      floor: 2,
      main: 2,
      isReversed: false,
      keywords: ['community', 'health', 'nursing', 'laboratory', 'lab'],
      aliases: ['nursing lab', 'community health lab'],
    ),
    Building1Location(
      id: 'f2_m3',
      name: 'Medical Surgical Laboratory',
      displayName: 'Medical Surgical Laboratory',
      floor: 2,
      main: 3,
      isReversed: false,
      keywords: ['medical', 'surgical', 'laboratory', 'lab', 'surgery'],
      aliases: ['surgical lab', 'medical lab'],
    ),
    Building1Location(
      id: 'f2_m4',
      name: 'Nursing Arts Laboratory 3',
      displayName: 'Nursing Arts Laboratory 3',
      floor: 2,
      main: 4,
      isReversed: false,
      keywords: ['nursing', 'arts', 'laboratory', 'lab', '3'],
      aliases: ['nursing lab 3', 'nursing arts lab'],
    ),
    Building1Location(
      id: 'f2_m5',
      name: 'Faculty Lounge',
      displayName: 'Faculty Lounge',
      floor: 2,
      main: 5,
      isReversed: false,
      keywords: ['faculty', 'lounge', 'teachers', 'staff'],
      aliases: ['faculty room', 'teachers lounge'],
    ),
    Building1Location(
      id: 'f2_m8',
      name: '2Main 8',
      displayName: '2Main 8',
      floor: 2,
      main: 8,
      isReversed: false,
      keywords: ['2main8', 'second', 'floor', 'corridor', 'stairs'],
      aliases: ['2main 8', 'second floor corridor 8', 'stairs area'],
    ),
    Building1Location(
      id: 'f2_m8_r',
      name: '2Main 8 Reversed',
      displayName: '2Main 8 (Reversed)',
      floor: 2,
      main: 8,
      isReversed: true,
      keywords: ['2main8', 'second', 'floor', 'corridor', 'reversed', 'stairs'],
      aliases: ['2main 8 reversed', 'second floor corridor 8 reversed'],
    ),
    Building1Location(
      id: 'f2_m9',
      name: '2Main 9',
      displayName: '2Main 9',
      floor: 2,
      main: 9,
      isReversed: false,
      keywords: ['2main9', 'second', 'floor', 'corridor', 'building', 'change'],
      aliases: ['2main 9', 'second floor corridor 9', 'building change area'],
    ),
    Building1Location(
      id: 'f2_m9_r',
      name: '2Main 9 Reversed',
      displayName: '2Main 9 (Reversed)',
      floor: 2,
      main: 9,
      isReversed: true,
      keywords: ['2main9', 'second', 'floor', 'corridor', 'reversed'],
      aliases: ['2main 9 reversed', 'second floor corridor 9 reversed'],
    ),

    // Floor 3 (3main0-3main25)
    Building1Location(
      id: 'f3_m0',
      name: 'CNAHS - 3rd Floor',
      displayName: 'CNAHS - 3rd Floor',
      floor: 3,
      main: 0,
      isReversed: false,
      keywords: ['cnahs', 'third', 'floor', '3rd', 'nursing'],
      aliases: ['cnahs 3rd floor', 'third floor cnahs'],
    ),
    Building1Location(
      id: 'f3_m1',
      name: 'Intensive Care Unit',
      displayName: 'Intensive Care Unit',
      floor: 3,
      main: 1,
      isReversed: false,
      keywords: ['intensive', 'care', 'unit', 'icu', 'medical'],
      aliases: ['icu', 'intensive care', 'critical care'],
    ),

    // Floor 4 (4main0-4main25)
    Building1Location(
      id: 'f4_m0',
      name: '4Main 0',
      displayName: '4Main 0',
      floor: 4,
      main: 0,
      isReversed: false,
      keywords: ['4main0', 'fourth', 'floor', 'corridor'],
      aliases: ['4main 0', 'fourth floor start'],
    ),
    Building1Location(
      id: 'f4_m1',
      name: '4Main 1',
      displayName: '4Main 1',
      floor: 4,
      main: 1,
      isReversed: false,
      keywords: ['4main1', 'fourth', 'floor', 'corridor'],
      aliases: ['4main 1', 'fourth floor corridor 1'],
    ),
    Building1Location(
      id: 'f4_m1_r',
      name: '4Main 1 Reversed',
      displayName: '4Main 1 (Reversed)',
      floor: 4,
      main: 1,
      isReversed: true,
      keywords: ['4main1', 'fourth', 'floor', 'corridor', 'reversed'],
      aliases: ['4main 1 reversed', 'fourth floor corridor 1 reversed'],
    ),
  ];

  static List<Building1Location> searchLocations(String query) {
    // Filter out reversed locations from search results (like Buildings 2 & 3)
    final nonReversedLocations =
        allLocations.where((location) => !location.isReversed).toList();

    if (query.isEmpty) return nonReversedLocations;

    final lowerQuery = query.toLowerCase();
    return nonReversedLocations.where((location) {
      return location.name.toLowerCase().contains(lowerQuery) ||
          location.displayName.toLowerCase().contains(lowerQuery) ||
          location.keywords
              .any((keyword) => keyword.toLowerCase().contains(lowerQuery)) ||
          location.aliases
              .any((alias) => alias.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  static Building1Location? findLocationByPosition(int floor, int main,
      {bool isReversed = false}) {
    try {
      return allLocations.firstWhere(
        (location) =>
            location.floor == floor &&
            location.main == main &&
            location.isReversed == isReversed,
      );
    } catch (e) {
      return null;
    }
  }

  static Building1Location? findLocationById(String id) {
    try {
      return allLocations.firstWhere((location) => location.id == id);
    } catch (e) {
      return null;
    }
  }

  static int getMaxMainForFloor(int floor) {
    switch (floor) {
      case 1:
      case 2:
      case 3:
      case 4:
        return 25; // All floors have main0-main25
      default:
        return 0;
    }
  }
}

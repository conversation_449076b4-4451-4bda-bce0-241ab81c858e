import 'dart:convert';
import 'package:intl/intl.dart';

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:test_1_copy_auth_events/services/auth_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:table_calendar/table_calendar.dart';

class EventsPage extends StatefulWidget {
  final bool isGuest;

  const EventsPage({Key? key, required this.isGuest}) : super(key: key);

  @override
  _EventsPageState createState() => _EventsPageState();
}

class _EventsPageState extends State<EventsPage>
    with AutomaticKeepAliveClientMixin {
  int _selectedEventIndex = 0;
  final AuthService _authService = AuthService();
  List<Map<String, dynamic>> allEvents = [];
  bool _isLoading = true;
  bool _dataLoaded = false; // Track if data is already loaded
  DateTime _selectedDay = DateTime.now();
  DateTime _focusedDay = DateTime.now();

  Future<void> _loadEventsOnce() async {
    final prefs = await SharedPreferences.getInstance();
    final cacheKey = widget.isGuest ? 'guest_events' : 'user_events';

    // Track last user who loaded events
    final currentEmail = _authService.userEmail ?? 'guest';
    final lastEventsEmail = prefs.getString('last_events_email');

    _dataLoaded = prefs.getBool('events_loaded_$cacheKey') ?? false;

    // If user changed, force reload
    if (currentEmail != lastEventsEmail) {
      await _loadEvents();
      await prefs.setString('last_events_email', currentEmail);
      await prefs.setBool('events_loaded_$cacheKey', true);
      return;
    }

    if (!_dataLoaded) {
      await _loadEvents();
      await prefs.setBool('events_loaded_$cacheKey', true);
      await prefs.setString('last_events_email', currentEmail);
    } else {
      // Load cached events
      final cachedEvents = prefs.getString('cached_events_$cacheKey');
      if (cachedEvents != null) {
        setState(() {
          allEvents = (json.decode(cachedEvents) as List).map((event) {
            return {
              ...(event as Map<String, dynamic>),
              'isExpanded': false,
            };
          }).toList();
          _isLoading = false;
        });
      } else {
        // If cache is empty, load fresh data
        await _loadEvents();
        await prefs.setBool('events_loaded_$cacheKey', true);
        await prefs.setString('last_events_email', currentEmail);
      }
    }
  }

  @override
  bool get wantKeepAlive => true; // This preserves state when switching tabs

  @override
  void initState() {
    super.initState();
    _loadCarouselPosition();
    _loadEventsOnce();
  }

  Future<void> _loadCarouselPosition() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _selectedEventIndex = prefs.getInt('last_event_index') ?? 0;
    });
  }

  Future<void> _saveCarouselPosition() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('last_event_index', _selectedEventIndex);
  }

  void _onPageChanged(int index) {
    setState(() {
      _selectedEventIndex = index;
    });
    _saveCarouselPosition();
  }

  Future<void> _cacheEvents() async {
    final prefs = await SharedPreferences.getInstance();
    final cacheKey = widget.isGuest ? 'guest_events' : 'user_events';

    await prefs.setString(
      'cached_events_$cacheKey',
      json.encode(allEvents.map((event) {
        // Create a copy without the isExpanded state for caching
        final Map<String, dynamic> eventCopy = Map.from(event);
        eventCopy.remove('isExpanded');
        return eventCopy;
      }).toList()),
    );
  }

  Future<void> _loadEvents() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final email = _authService.userEmail;
      if (email != null && !widget.isGuest) {
        // Load events only if not a guest
        final userDetails = await _authService.fetchUserDetails(email);
        if (userDetails != null) {
          final events = await _authService.fetchEvents(
            userDetails['department'],
            userDetails['course'],
          );

          // Sort events based on date and time
          events.sort((a, b) {
            DateTime dateA = DateTime.parse(a['date']);
            DateTime dateB = DateTime.parse(b['date']);
            return dateA.compareTo(dateB);
          });

          setState(() {
            allEvents = events.map((event) {
              return {
                ...event,
                'isExpanded': false,
              };
            }).toList();
          });
        }
      } else {
        // Load guest events
        final guestEvents =
            await _authService.fetchEvents('', '', isGuest: true);

        // Filter guest events to only include those with forguest = 'y'
        final filteredGuestEvents =
            guestEvents.where((event) => event['forguest'] == 'y').toList();

        // Sort guest events based on date and time
        filteredGuestEvents.sort((a, b) {
          DateTime dateA = DateTime.parse(a['date']);
          DateTime dateB = DateTime.parse(b['date']);
          return dateA.compareTo(dateB);
        });

        setState(() {
          allEvents = filteredGuestEvents.map((event) {
            return {
              ...event,
              'isExpanded': false,
            };
          }).toList();
        });
      }

      // Cache the loaded events
      await _cacheEvents();
    } catch (e) {
      print('Error loading events: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

// First, modify your _handleRefresh method to save the current position before refreshing
  Future<void> _handleRefresh() async {
    // Store current event index before refreshing
    int currentPosition = _selectedEventIndex;

    // Clear the loaded flag to force a fresh load
    final prefs = await SharedPreferences.getInstance();
    final cacheKey = widget.isGuest ? 'guest_events' : 'user_events';
    await prefs.setBool('events_loaded_$cacheKey', false);

    await _loadEvents();

    // After loading new events, restore the carousel position
    // We need to wait for the state to be updated
    setState(() {
      _selectedEventIndex = currentPosition;
    });

    return Future.value();
  }

  String getEventImageUrl(String eventName) {
    String baseName = eventName.split('.').first;
    return _authService.client.storage
        .from('events')
        .getPublicUrl('$baseName.jpg');
  }

  List<TextSpan> _getDescriptionSpans(String description) {
    // Improved regex to better match URLs
    final urlRegex = RegExp(
      r'(?:(?:https?:\/\/)?(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z0-9.-]+(?:\/[^\s]*)?)',
      caseSensitive: false,
    );

    List<TextSpan> spans = [];
    final matches = urlRegex.allMatches(description);
    int lastIndex = 0;

    for (final match in matches) {
      final rawUrl = match.group(0)!;
      final urlStart = match.start;

      // Add protocol if missing
      final url = rawUrl.startsWith('http') ? rawUrl : 'https://$rawUrl';

      // Add text before the URL
      if (urlStart > lastIndex) {
        spans.add(TextSpan(text: description.substring(lastIndex, urlStart)));
      }

      // Add the URL as a tappable TextSpan
      spans.add(
        TextSpan(
          text: rawUrl, // Display the original URL text
          style: const TextStyle(
              color: Colors.blue, decoration: TextDecoration.underline),
          recognizer: TapGestureRecognizer()
            ..onTap = () async {
              try {
                final Uri uri = Uri.parse(url); // Parse with added protocol
                if (await canLaunchUrl(uri)) {
                  await launchUrl(uri, mode: LaunchMode.externalApplication);
                } else {
                  print('Could not launch $url');
                }
              } catch (e) {
                print('Error launching URL: $e');
              }
            },
        ),
      );

      lastIndex = match.end;
    }

    // Add any remaining text after the last URL
    if (lastIndex < description.length) {
      spans.add(TextSpan(text: description.substring(lastIndex)));
    }

    return spans;
  }

  void _showCalendarDialog() {
    Map<DateTime, List<Map<String, dynamic>>> eventMap = {};

    // Group events by date
    for (var event in allEvents) {
      DateTime eventDate = DateTime.parse(event['date']);
      DateTime normalizedDate = DateTime(
        eventDate.year,
        eventDate.month,
        eventDate.day,
      );

      if (eventMap[normalizedDate] == null) {
        eventMap[normalizedDate] = [];
      }
      eventMap[normalizedDate]!.add(event);
    }

    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            padding: EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Events Calendar',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                SizedBox(height: 16),
                Container(
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: StatefulBuilder(
                    builder: (context, setState) {
                      DateTime normalizedSelectedDay = DateTime(
                        _selectedDay.year,
                        _selectedDay.month,
                        _selectedDay.day,
                      );

                      return Column(
                        children: [
                          TableCalendar(
                            firstDay: DateTime.utc(2020, 1, 1),
                            lastDay: DateTime.utc(2030, 12, 31),
                            focusedDay: _focusedDay,
                            selectedDayPredicate: (day) {
                              return isSameDay(_selectedDay, day);
                            },
                            onDaySelected: (selectedDay, focusedDay) {
                              setState(() {
                                _selectedDay = selectedDay;
                                _focusedDay = focusedDay;
                              });
                            },
                            calendarStyle: CalendarStyle(
                              todayDecoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.5),
                                shape: BoxShape.circle,
                              ),
                              selectedDecoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                              ),
                              markerDecoration: BoxDecoration(
                                color: Colors.green.shade800,
                                shape: BoxShape.circle,
                              ),
                            ),
                            headerStyle: HeaderStyle(
                              formatButtonVisible: false,
                              titleCentered: true,
                            ),
                            eventLoader: (day) {
                              DateTime normalizedDay =
                                  DateTime(day.year, day.month, day.day);
                              return eventMap[normalizedDay] ?? [];
                            },
                          ),
                          SizedBox(height: 16),
                          Expanded(
                            child: _buildEventList(
                                eventMap[normalizedSelectedDay] ?? []),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('Close'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEventList(List<Map<String, dynamic>> dayEvents) {
    if (dayEvents.isEmpty) {
      return Center(
        child: Text('No events on this day'),
      );
    }

    return ListView.builder(
      itemCount: dayEvents.length,
      itemBuilder: (context, index) {
        final event = dayEvents[index];
        return ListTile(
          title: Text(
            event['name'],
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(event['location']),
              Text(formatDate(event['date'])),
            ],
          ),
          onTap: () {
            // Show event details in a dialog
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Text(event['name']),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Location: ${event['location']}'),
                    Text('Date: ${formatDate(event['date'])}'),
                    SizedBox(height: 8),
                    Text.rich(
                      TextSpan(
                        children: _getDescriptionSpans(event['description']),
                      ),
                    ),
                  ],
                ),
                actions: [
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Close'),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentDate = DateTime.now();
    super.build(context);

    // Today's events
    final todayEvents = allEvents.where((event) {
      DateTime eventDate = DateTime.parse(event['date']);
      return eventDate.year == currentDate.year &&
          eventDate.month == currentDate.month &&
          eventDate.day == currentDate.day;
    }).toList();

    // Show only events from tomorrow onwards in the current month
    final upcomingEvents = allEvents.where((event) {
      DateTime eventDate = DateTime.parse(event['date']);
      DateTime tomorrow =
          DateTime(currentDate.year, currentDate.month, currentDate.day + 1);

      // Only include events from tomorrow onwards in the current month
      return eventDate.year == currentDate.year &&
          eventDate.month == currentDate.month &&
          eventDate.isAfter(DateTime(currentDate.year, currentDate.month,
              currentDate.day, 23, 59, 59));
    }).toList();

    // Reset _selectedEventIndex if it's out of bounds
    if (todayEvents.isEmpty) {
      _selectedEventIndex = 0;
    } else if (_selectedEventIndex >= todayEvents.length) {
      _selectedEventIndex = todayEvents.length - 1;
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Campus Events',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 30),
        ),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.history),
            onPressed: _showHistoryDialog,
            tooltip: 'Past Events',
          ),
          IconButton(
            icon: Icon(Icons.calendar_month),
            onPressed: _showCalendarDialog,
            tooltip: 'Calendar View',
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green)))
          : RefreshIndicator(
              color: Colors.green,
              onRefresh: _handleRefresh,
              child: SingleChildScrollView(
                physics:
                    AlwaysScrollableScrollPhysics(), // Important for RefreshIndicator to work
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 5),
                      Text(
                        'Events Today',
                        style: TextStyle(
                          fontSize: 25,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 10),
                      if (todayEvents.isNotEmpty) ...[
                        Container(
                          height: 350,
                          child: PageView.builder(
                            itemCount: todayEvents.length,
                            controller: PageController(
                                initialPage: _selectedEventIndex),
                            onPageChanged: _onPageChanged,
                            itemBuilder: (context, index) {
                              final event = todayEvents[index];
                              return Stack(
                                children: [
                                  Container(
                                    height:
                                        325, // Set a fixed height for all images
                                    width: double
                                        .infinity, // Make image fill the width of the parent
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    clipBehavior: Clip.hardEdge,
                                    child: Image.network(
                                      getEventImageUrl(event['name']),
                                      fit: BoxFit
                                          .cover, // Ensures the image fills the box and crops as needed
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Image.network(
                                          getEventImageUrl('DEFAULT'),
                                          fit: BoxFit.cover,
                                        );
                                      },
                                    ),
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Colors.transparent,
                                          Colors.black.withOpacity(0),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                        // Add carousel indicator dots
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(
                            todayEvents.length,
                            (index) => Container(
                              width: 8,
                              height: 8,
                              margin: EdgeInsets.symmetric(horizontal: 4),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _selectedEventIndex == index
                                    ? Colors.green
                                    : Colors.grey.withOpacity(0.5),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          todayEvents[_selectedEventIndex]['name'],
                          style: TextStyle(
                            fontSize: 25,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          todayEvents[_selectedEventIndex]['location'],
                          style: TextStyle(
                            fontSize: 17,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          formatDate(todayEvents[_selectedEventIndex]['date']),
                          style: TextStyle(
                            fontSize: 15,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 16),
                        Text.rich(
                          TextSpan(
                            children: _getDescriptionSpans(
                                todayEvents[_selectedEventIndex]
                                    ['description']),
                          ),
                          style: TextStyle(fontSize: 17),
                          maxLines: todayEvents[_selectedEventIndex]
                                  ['isExpanded']
                              ? null
                              : 3,
                          overflow: todayEvents[_selectedEventIndex]
                                  ['isExpanded']
                              ? TextOverflow.visible
                              : TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 8),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              todayEvents[_selectedEventIndex]['isExpanded'] =
                                  !todayEvents[_selectedEventIndex]
                                      ['isExpanded'];
                            });
                          },
                          child: Text(
                            todayEvents[_selectedEventIndex]['isExpanded']
                                ? 'Show Less'
                                : 'See More',
                            style: TextStyle(
                              color: Colors.green,
                              fontSize: 15,
                            ),
                          ),
                        ),
                      ] else
                        Text('No events today',
                            style: TextStyle(fontSize: 16, color: Colors.grey)),
                      SizedBox(height: 25),
                      Text(
                        'Upcoming Events (${DateFormat('MMMM').format(currentDate)})',
                        style: TextStyle(
                          fontSize: 25,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 15),
                      Container(
                        // Dynamically adjust height based on number of events
                        height: upcomingEvents.isEmpty
                            ? 100 // Minimal height when empty
                            : upcomingEvents.length <= 1
                                ? 250 // Smaller height for 1 event
                                : 400, // Larger height for 2+ events
                        child: upcomingEvents.isEmpty
                            ? Center(
                                child: Text('No upcoming events this month',
                                    style: TextStyle(
                                        fontSize: 17, color: Colors.grey)))
                            : ListView.builder(
                                physics: BouncingScrollPhysics(),
                                itemCount: upcomingEvents.length,
                                itemBuilder: (context, index) {
                                  final event = upcomingEvents[index];
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 4.0),
                                    child: _buildEventCard(
                                      title: event['name'],
                                      location: event['location'],
                                      date: formatDate(event['date']),
                                      description: event['description'],
                                      imageUrl: getEventImageUrl(event['name']),
                                    ),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  // Add this method to check if an image exists for an event
  Future<bool> doesEventImageExist(String eventName) async {
    try {
      String baseName = eventName.split('.').first;
      // List files in the events bucket to check if the image exists
      final response = await _authService.client.storage.from('events').list();

      // Check if the file exists in the events directory
      return response.any(
          (file) => file.name == '$baseName.jpg' || file.name == 'DEFAULT.jpg');
    } catch (e) {
      print('Error checking if event image exists: $e');
      return false;
    }
  }

  Widget _buildEventCard({
    required String title,
    required String location,
    required String date,
    required String description,
    required String imageUrl,
  }) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          FutureBuilder<bool>(
            future: doesEventImageExist(title),
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data == true) {
                return Container(
                  height: 150,
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(10)),
                    image: DecorationImage(
                      image: NetworkImage(getEventImageUrl(title)),
                      fit: BoxFit.cover,
                      onError: (error, stackTrace) {
                        print('Error loading image: $error');
                      },
                    ),
                  ),
                );
              } else {
                // Return empty container when no image exists
                return SizedBox.shrink();
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                  ),
                ),
                SizedBox(height: 5),
                Text(
                  location,
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 5),
                Text(
                  date,
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  description,
                  style: TextStyle(fontSize: 15),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Add this new method for history dialog
  void _showHistoryDialog() {
    final currentDate = DateTime.now();

    // Filter for past events in the current month
    final pastEvents = allEvents.where((event) {
      DateTime eventDate = DateTime.parse(event['date']);
      DateTime startOfToday =
          DateTime(currentDate.year, currentDate.month, currentDate.day);
      return eventDate.year == currentDate.year &&
          eventDate.month == currentDate.month &&
          eventDate.isBefore(startOfToday); // Include past events
    }).toList();

    // Sort past events by date (earliest first)
    pastEvents.sort((a, b) {
      DateTime dateA = DateTime.parse(a['date']);
      DateTime dateB = DateTime.parse(b['date']);
      return dateA.compareTo(dateB);
    });

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'Past Events (${DateFormat('MMMM').format(currentDate)})',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          content: Container(
            width: double.maxFinite,
            child: pastEvents.isEmpty
                ? Center(child: Text('No past events this month'))
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: pastEvents.length,
                    itemBuilder: (context, index) {
                      final event = pastEvents[index];
                      return ListTile(
                        title: Text(
                          event['name'],
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(event['location']),
                            Text(formatDate(event['date'])),
                          ],
                        ),
                        onTap: () {
                          // Show event details in a dialog
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(event['name']),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Location: ${event['location']}'),
                                  Text('Date: ${formatDate(event['date'])}'),
                                  SizedBox(height: 8),
                                  Text.rich(
                                    TextSpan(
                                      children: _getDescriptionSpans(
                                          event['description']),
                                    ),
                                  ),
                                ],
                              ),
                              actions: [
                                ElevatedButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: Text('Close'),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Close', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}

String formatDate(String isoDate) {
  DateTime dateTime = DateTime.parse(isoDate);
  String hour = dateTime.hour > 12
      ? (dateTime.hour - 12).toString()
      : dateTime.hour.toString();
  String minute = dateTime.minute.toString().padLeft(2, '0');
  String amPm = dateTime.hour >= 12 ? 'PM' : 'AM';
  String formattedDate =
      "${dateTime.day}/${dateTime.month}/${dateTime.year} $hour:$minute $amPm";
  return formattedDate;
}

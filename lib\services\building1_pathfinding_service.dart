import '../models/building1_location.dart';

enum Building1NavigationDirection {
  forward,
  backward,
  reverse,
  upFloor,
  downFloor,
  turnLeft,
  turnRight,
  arrived,
}

class Building1NavigationStep {
  final Building1Location from;
  final Building1Location to;
  final Building1NavigationDirection direction;
  final String instruction;
  final int stepNumber;

  Building1NavigationStep({
    required this.from,
    required this.to,
    required this.direction,
    required this.instruction,
    required this.stepNumber,
  });

  @override
  String toString() => instruction;
}

class Building1NavigationRoute {
  final List<Building1NavigationStep> steps;
  final Building1Location destination;
  final int totalSteps;

  Building1NavigationRoute({
    required this.steps,
    required this.destination,
  }) : totalSteps = steps.length;

  bool get isEmpty => steps.isEmpty;
  bool get isNotEmpty => steps.isNotEmpty;

  Building1NavigationStep? getCurrentStep(Building1Location currentLocation) {
    for (int i = 0; i < steps.length; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.main == currentLocation.main &&
          steps[i].from.isReversed == currentLocation.isReversed) {
        return steps[i];
      }
    }
    return null;
  }

  Building1NavigationStep? getNextStep(Building1Location currentLocation) {
    for (int i = 0; i < steps.length - 1; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.main == currentLocation.main &&
          steps[i].from.isReversed == currentLocation.isReversed) {
        return steps[i + 1];
      }
    }
    return null;
  }

  int getProgressPercentage(Building1Location currentLocation) {
    if (steps.isEmpty) return 100;

    for (int i = 0; i < steps.length; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.main == currentLocation.main &&
          steps[i].from.isReversed == currentLocation.isReversed) {
        return ((i + 1) / steps.length * 100).round();
      }
    }

    // Check if user has arrived at destination
    if (currentLocation.floor == destination.floor &&
        currentLocation.main == destination.main) {
      return 100;
    }

    return 0;
  }
}

class Building1PathfindingService {
  static const int _moveCost = 1;
  static const int _floorChangeCost = 3;
  static const int _reverseCost = 2;
  static const int _turnCost = 1;

  static Building1NavigationRoute? findShortestPath(
    Building1Location start,
    Building1Location destination, {
    bool isStartReversed = false,
  }) {
    if (start == destination) {
      return Building1NavigationRoute(steps: [], destination: destination);
    }

    // Use Dijkstra's algorithm for pathfinding
    final distances = <String, int>{};
    final previous = <String, Building1Location?>{};
    final unvisited = <Building1Location>[];

    // Initialize distances
    for (final location in Building1LocationData.allLocations) {
      distances[location.id] = double.maxFinite.toInt();
      previous[location.id] = null;
      unvisited.add(location);
    }

    // Set start distance
    final startLocationId =
        _getLocationId(start.floor, start.main, isStartReversed);
    final actualStart = Building1LocationData.findLocationById(startLocationId);
    if (actualStart == null) return null;

    distances[actualStart.id] = 0;

    while (unvisited.isNotEmpty) {
      // Find unvisited location with minimum distance
      Building1Location? current;
      int minDistance = double.maxFinite.toInt();

      for (final location in unvisited) {
        final distance = distances[location.id] ?? double.maxFinite.toInt();
        if (distance < minDistance) {
          minDistance = distance;
          current = location;
        }
      }

      if (current == null || minDistance == double.maxFinite.toInt()) {
        break; // No path found
      }

      unvisited.remove(current);

      // Check if we reached the destination
      if (current.floor == destination.floor &&
          current.main == destination.main) {
        break;
      }

      // Check neighbors
      final neighbors = _getNeighbors(current);
      for (final neighbor in neighbors) {
        if (!unvisited.contains(neighbor)) continue;

        final newDistance =
            distances[current.id]! + _calculateMoveCost(current, neighbor);
        if (newDistance <
            (distances[neighbor.id] ?? double.maxFinite.toInt())) {
          distances[neighbor.id] = newDistance;
          previous[neighbor.id] = current;
        }
      }
    }

    // Reconstruct path
    final path = <Building1Location>[];
    Building1Location? current = destination;

    while (current != null) {
      path.insert(0, current);
      current = previous[current.id];
    }

    if (path.isEmpty || path.first.id != actualStart.id) {
      return null; // No path found
    }

    // Convert path to navigation steps
    final steps = <Building1NavigationStep>[];
    bool userFacingReversed = isStartReversed;

    for (int i = 0; i < path.length - 1; i++) {
      final from = path[i];
      final to = path[i + 1];
      final previousLocation = i > 0 ? path[i - 1] : null;

      final direction = _getDirectionWithContext(
        from,
        to,
        previousLocation,
        userFacingReversed,
        fullPath: path,
        currentStepIndex: i,
      );

      // If user turns around, update their facing direction for subsequent steps
      if (direction == Building1NavigationDirection.reverse) {
        userFacingReversed = !userFacingReversed;
      }

      final instruction = _getInstruction(from, to, direction);

      steps.add(Building1NavigationStep(
        from: from,
        to: to,
        direction: direction,
        instruction: instruction,
        stepNumber: i + 1,
      ));
    }

    return Building1NavigationRoute(steps: steps, destination: destination);
  }

  static List<Building1Location> _getNeighbors(Building1Location location) {
    final neighbors = <Building1Location>[];

    // Same floor movement - forward/backward
    if (location.main > 0) {
      final backward = Building1LocationData.findLocationByPosition(
          location.floor, location.main - 1,
          isReversed: location.isReversed);
      if (backward != null) neighbors.add(backward);
    }

    if (location.main <
        Building1LocationData.getMaxMainForFloor(location.floor)) {
      final forward = Building1LocationData.findLocationByPosition(
          location.floor, location.main + 1,
          isReversed: location.isReversed);
      if (forward != null) neighbors.add(forward);
    }

    // Reverse direction (turn around)
    final reversed = Building1LocationData.findLocationByPosition(
        location.floor, location.main,
        isReversed: !location.isReversed);
    if (reversed != null) neighbors.add(reversed);

    // Floor changes (stairs) - based on Building 1 navigation logic
    if (_canChangeFloor(location)) {
      // Go up
      if (location.floor < 4) {
        final upFloor = _getFloorChangeDestination(location, true);
        if (upFloor != null) neighbors.add(upFloor);
      }

      // Go down
      if (location.floor > 1) {
        final downFloor = _getFloorChangeDestination(location, false);
        if (downFloor != null) neighbors.add(downFloor);
      }
    }

    // Turn options - based on Building 1 navigation logic
    final turnOptions = _getTurnOptions(location);
    neighbors.addAll(turnOptions);

    return neighbors;
  }

  static bool _canChangeFloor(Building1Location location) {
    // Based on Building 1 navigation logic for stairs
    return (location.main == 8 && location.floor == 1) || // main8 to 2main8_r
        (location.main == 8 && location.floor == 2) || // 2main8 to 3main0
        (location.main == 15 && location.floor == 1) || // main15 to 2main1
        (location.main == 1 && location.floor == 2) || // 2main1_r to main15_r
        (location.main == 0 &&
            location.floor == 3) || // 3main0 to 2main8 or 4main1_r
        (location.main == 1 && location.floor == 4); // 4main1_r to 3main0
  }

  static Building1Location? _getFloorChangeDestination(
      Building1Location location, bool goingUp) {
    if (goingUp) {
      if (location.floor == 1 && location.main == 8) {
        return Building1LocationData.findLocationByPosition(2, 8,
            isReversed: true);
      } else if (location.floor == 1 && location.main == 15) {
        return Building1LocationData.findLocationByPosition(2, 1,
            isReversed: false);
      } else if (location.floor == 2 && location.main == 8) {
        return Building1LocationData.findLocationByPosition(3, 0,
            isReversed: false);
      } else if (location.floor == 3 && location.main == 0) {
        return Building1LocationData.findLocationByPosition(4, 1,
            isReversed: true);
      }
    } else {
      if (location.floor == 2 && location.main == 1 && location.isReversed) {
        return Building1LocationData.findLocationByPosition(1, 15,
            isReversed: true);
      } else if (location.floor == 3 && location.main == 0) {
        return Building1LocationData.findLocationByPosition(2, 8,
            isReversed: false);
      } else if (location.floor == 4 &&
          location.main == 1 &&
          location.isReversed) {
        return Building1LocationData.findLocationByPosition(3, 0,
            isReversed: false);
      }
    }
    return null;
  }

  static List<Building1Location> _getTurnOptions(Building1Location location) {
    final turnOptions = <Building1Location>[];

    // Based on Building 1 navigation turn logic
    if (!location.isReversed) {
      // Normal mode turns
      if (location.main == 4) {
        // Can turn left to main5 or right (with conditional choices)
        final leftTurn = Building1LocationData.findLocationByPosition(
            location.floor, 5,
            isReversed: false);
        if (leftTurn != null) turnOptions.add(leftTurn);
      } else if (location.main == 8) {
        // Can turn right to main9
        final rightTurn = Building1LocationData.findLocationByPosition(
            location.floor, 9,
            isReversed: false);
        if (rightTurn != null) turnOptions.add(rightTurn);
      } else if (location.main == 11) {
        // Can turn right to main12
        final rightTurn = Building1LocationData.findLocationByPosition(
            location.floor, 12,
            isReversed: false);
        if (rightTurn != null) turnOptions.add(rightTurn);
      }
    } else {
      // Reversed mode turns
      if (location.main == 5) {
        // Can turn right to main4_r or left (with conditional choices)
        final rightTurn = Building1LocationData.findLocationByPosition(
            location.floor, 4,
            isReversed: true);
        if (rightTurn != null) turnOptions.add(rightTurn);
      } else if (location.main == 9 && location.floor != 2) {
        // Can turn left to main8_r (except on floor 2)
        final leftTurn = Building1LocationData.findLocationByPosition(
            location.floor, 8,
            isReversed: true);
        if (leftTurn != null) turnOptions.add(leftTurn);
      } else if (location.main == 12) {
        // Can turn left to main11_r
        final leftTurn = Building1LocationData.findLocationByPosition(
            location.floor, 11,
            isReversed: true);
        if (leftTurn != null) turnOptions.add(leftTurn);
      }
    }

    return turnOptions;
  }

  static String _getLocationId(int floor, int main, bool isReversed) {
    return 'f${floor}_m$main${isReversed ? '_r' : ''}';
  }

  static int _calculateMoveCost(Building1Location from, Building1Location to) {
    if (from.floor != to.floor) {
      return _floorChangeCost;
    } else if (from.main != to.main) {
      return _moveCost;
    } else if (from.isReversed != to.isReversed) {
      return _reverseCost;
    } else {
      return _turnCost;
    }
  }

  // Check if we should suggest "turn around and move forward" instead of "move backward"
  // Based on Buildings 2 & 3 logic
  static bool _shouldSuggestTurnAround(List<Building1Location> fullPath,
      int currentStepIndex, bool isInReversedView) {
    if (currentStepIndex >= fullPath.length - 1) return false;

    final from = fullPath[currentStepIndex];
    final to = fullPath[currentStepIndex + 1];

    // Only consider turn-around for same-floor movements
    if (from.floor != to.floor) return false;

    // Determine what the natural movement direction would be
    bool wouldMoveBackward = false;
    if (isInReversedView) {
      // In reversed view, moving to higher main requires moving backward
      wouldMoveBackward = to.main > from.main;
    } else {
      // In normal view, moving to lower main requires moving backward
      wouldMoveBackward = to.main < from.main;
    }

    // If this step doesn't require moving backward, no need to turn around
    if (!wouldMoveBackward) return false;

    // Count consecutive backward movements in the same direction
    int consecutiveBackwardSteps = 0;
    int checkIndex = currentStepIndex;

    while (checkIndex < fullPath.length - 1) {
      final checkFrom = fullPath[checkIndex];
      final checkTo = fullPath[checkIndex + 1];

      // Stop if we change floors
      if (checkFrom.floor != checkTo.floor) break;

      // Check if this step also requires moving backward
      bool thisStepBackward = false;
      if (isInReversedView) {
        thisStepBackward = checkTo.main > checkFrom.main;
      } else {
        thisStepBackward = checkTo.main < checkFrom.main;
      }

      if (thisStepBackward) {
        consecutiveBackwardSteps++;
        checkIndex++;
      } else {
        break;
      }
    }

    // Suggest turn around if we have multiple consecutive backward steps
    // This makes navigation more intuitive
    return consecutiveBackwardSteps >= 2;
  }

  static Building1NavigationDirection _getDirectionWithContext(
      Building1Location from,
      Building1Location to,
      Building1Location? previousLocation,
      bool isCurrentlyReversed,
      {required List<Building1Location> fullPath,
      required int currentStepIndex}) {
    // Floor changes
    if (from.floor != to.floor) {
      return to.floor > from.floor
          ? Building1NavigationDirection.upFloor
          : Building1NavigationDirection.downFloor;
    }

    // Turn around (reverse direction)
    if (from.main == to.main && from.isReversed != to.isReversed) {
      return Building1NavigationDirection.reverse;
    }

    // Turn options
    if (_isTurnMovement(from, to)) {
      return _getTurnDirection(from, to);
    }

    // Check if we should suggest "turn around and move forward" instead of "move backward"
    if (_shouldSuggestTurnAround(
        fullPath, currentStepIndex, isCurrentlyReversed)) {
      return Building1NavigationDirection.reverse;
    }

    // Regular movement
    if (from.main != to.main) {
      if (isCurrentlyReversed) {
        return to.main < from.main
            ? Building1NavigationDirection.forward
            : Building1NavigationDirection.backward;
      } else {
        return to.main > from.main
            ? Building1NavigationDirection.forward
            : Building1NavigationDirection.backward;
      }
    }

    return Building1NavigationDirection.forward;
  }

  static bool _isTurnMovement(Building1Location from, Building1Location to) {
    // Check if this is a turn movement based on Building 1 navigation logic
    if (!from.isReversed) {
      return (from.main == 4 && to.main == 5) || // Turn left from main4
          (from.main == 8 && to.main == 9) || // Turn right from main8
          (from.main == 11 && to.main == 12); // Turn right from main11
    } else {
      return (from.main == 5 && to.main == 4) || // Turn right from main5_r
          (from.main == 9 && to.main == 8) || // Turn left from main9_r
          (from.main == 12 && to.main == 11); // Turn left from main12_r
    }
  }

  static Building1NavigationDirection _getTurnDirection(
      Building1Location from, Building1Location to) {
    if (!from.isReversed) {
      if (from.main == 4 && to.main == 5) {
        return Building1NavigationDirection.turnLeft;
      }
      if (from.main == 8 && to.main == 9) {
        return Building1NavigationDirection.turnRight;
      }
      if (from.main == 11 && to.main == 12) {
        return Building1NavigationDirection.turnRight;
      }
    } else {
      if (from.main == 5 && to.main == 4) {
        return Building1NavigationDirection.turnRight;
      }
      if (from.main == 9 && to.main == 8) {
        return Building1NavigationDirection.turnLeft;
      }
      if (from.main == 12 && to.main == 11) {
        return Building1NavigationDirection.turnLeft;
      }
    }
    return Building1NavigationDirection.forward;
  }

  static String _getInstruction(Building1Location from, Building1Location to,
      Building1NavigationDirection direction) {
    switch (direction) {
      case Building1NavigationDirection.forward:
        return 'Move forward';
      case Building1NavigationDirection.backward:
        return 'Move backward';
      case Building1NavigationDirection.reverse:
        return 'Turn around and move forward';
      case Building1NavigationDirection.upFloor:
        return 'Go upstairs to ${_getFloorName(to.floor)}';
      case Building1NavigationDirection.downFloor:
        return 'Go downstairs to ${_getFloorName(to.floor)}';
      case Building1NavigationDirection.turnLeft:
        return 'Turn left';
      case Building1NavigationDirection.turnRight:
        return 'Turn right';
      case Building1NavigationDirection.arrived:
        return 'You have arrived at ${to.displayName}';
    }
  }

  static String _getFloorName(int floor) {
    switch (floor) {
      case 1:
        return 'First Floor';
      case 2:
        return 'Second Floor';
      case 3:
        return 'Third Floor';
      case 4:
        return 'Fourth Floor';
      default:
        return 'Floor $floor';
    }
  }

  static String getDetailedInstruction(Building1NavigationStep step) {
    final baseInstruction = step.instruction;
    final destination = step.to.displayName;

    switch (step.direction) {
      case Building1NavigationDirection.forward:
        return 'Move forward to $destination';
      case Building1NavigationDirection.backward:
        return 'Move backward to $destination';
      case Building1NavigationDirection.reverse:
        return 'Turn around and move forward to $destination';
      case Building1NavigationDirection.upFloor:
      case Building1NavigationDirection.downFloor:
        return baseInstruction;
      case Building1NavigationDirection.turnLeft:
        return 'Turn left to $destination';
      case Building1NavigationDirection.turnRight:
        return 'Turn right to $destination';
      case Building1NavigationDirection.arrived:
        return baseInstruction;
    }
  }
}

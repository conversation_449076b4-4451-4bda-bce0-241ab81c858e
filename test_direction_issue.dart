import 'lib/models/building2_location.dart';
import 'lib/services/building2_pathfinding_service.dart';

void main() {
  print('=== Testing Building 2 Direction Issues ===\n');

  // Test Case 1: Human Resources (reversed) to Chemistry Lab 2
  print('Test 1: Human Resources (2clage1_r) to Chemistry Lab 2 (3clage2)');
  testRoute(2, 1, true, 3, 2, false, 'Human Resources (reversed) to Chem 2');

  print('\n' + '=' * 60 + '\n');

  // Test Case 2: Chemistry Lab 1 (reversed) to CEDE Faculty Room
  print('Test 2: Chemistry Lab 1 (3clage1_r) to CEDE Faculty Room (2clage3)');
  testRoute(3, 1, true, 2, 3, false, 'Chem 1 (reversed) to CEDE Faculty');

  print('\n' + '=' * 60 + '\n');

  // Test Case 3: Test using main stairs - Human Resources (normal) to Chemistry Lab 2
  print(
      'Test 3: Human Resources (2clage1) to Chemistry Lab 2 (3clage2) - using main stairs');
  testRoute(2, 1, false, 3, 2, false, 'HR (normal) to Chem 2 via main stairs');

  print('\n' + '=' * 60 + '\n');

  // Test Case 4: Test going down using alternative stairs
  print(
      'Test 4: Chemistry Lab 1 (3clage1) to Human Resources (2clage1) - using alternative stairs');
  testRoute(3, 1, false, 2, 1, false,
      'Chem 1 (normal) to HR (normal) via alt stairs');
}

void testRoute(int fromFloor, int fromClage, bool fromReversed, int toFloor,
    int toClage, bool toReversed, String description) {
  final fromLocation = Building2LocationData.findLocationByPosition(
      fromFloor, fromClage,
      isReversed: fromReversed);
  final toLocation = Building2LocationData.findLocationByPosition(
      toFloor, toClage,
      isReversed: toReversed);

  if (fromLocation == null || toLocation == null) {
    print('Error: Could not find locations');
    print('From: Floor $fromFloor, Clage $fromClage, Reversed: $fromReversed');
    print('To: Floor $toFloor, Clage $toClage, Reversed: $toReversed');
    return;
  }

  print(
      'From: ${fromLocation.displayName} (Floor ${fromLocation.floor}, Clage ${fromLocation.clage}, Reversed: ${fromLocation.isReversed})');
  print(
      'To: ${toLocation.displayName} (Floor ${toLocation.floor}, Clage ${toLocation.clage}, Reversed: ${toLocation.isReversed})');

  final route = Building2PathfindingService.findShortestPath(
    fromLocation,
    toLocation,
    isStartReversed: fromReversed,
  );

  if (route == null) {
    print('No route found!');
    return;
  }

  print('\nRoute steps:');
  for (int i = 0; i < route.steps.length; i++) {
    final step = route.steps[i];
    print('${i + 1}. ${step.instruction}');
    print(
        '   From: ${step.from.displayName} (Floor ${step.from.floor}, Clage ${step.from.clage})');
    print(
        '   To: ${step.to.displayName} (Floor ${step.to.floor}, Clage ${step.to.clage})');
    print('   Direction: ${step.direction}');
  }
}
